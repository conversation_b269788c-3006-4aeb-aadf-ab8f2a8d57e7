{"swagger": "2.0", "info": {"description": "This is the SCS User Service API for managing users and authentication", "title": "SCS User Service API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "API Support", "url": "http://www.swagger.io/support", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1.0"}, "host": "localhost:8080", "basePath": "/api/v1", "paths": {"/auth/login": {"post": {"description": "Authenticate user with email and password", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "User login", "parameters": [{"description": "Login credentials", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.LoginRequest"}}], "responses": {"200": {"description": "Login successful", "schema": {"$ref": "#/definitions/dto.LoginResponse"}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/auth/validate-token": {"post": {"description": "Validate if a JWT token is valid and not expired", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Validate JWT token", "parameters": [{"description": "Token to validate", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.ValidateTokenRequest"}}], "responses": {"200": {"description": "Token validation result", "schema": {"$ref": "#/definitions/dto.ValidateTokenResponse"}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/health": {"get": {"description": "Check if the service is running and healthy", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["health"], "summary": "Health check", "responses": {"200": {"description": "Service is healthy", "schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}}, "/users": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a paginated list of all users", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Get paginated list of users", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Number of items per page", "name": "limit", "in": "query"}], "responses": {"200": {"description": "Users retrieved successfully", "schema": {"$ref": "#/definitions/dto.UserListResponse"}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new user account with the provided information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Create a new user", "parameters": [{"description": "User creation data", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.CreateUserDto"}}], "responses": {"201": {"description": "User created successfully", "schema": {"$ref": "#/definitions/models.User"}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "409": {"description": "User already exists", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/users/me": {"get": {"security": [{"BearerAuth": []}], "description": "Get the profile information of the currently authenticated user", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Get current user profile", "responses": {"200": {"description": "User profile retrieved successfully", "schema": {"$ref": "#/definitions/models.User"}}, "401": {"description": "Unauthorized", "schema": {"type": "object", "additionalProperties": true}}, "404": {"description": "User not found", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}, "/users/verify": {"post": {"description": "Verify a user account using a verification token", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["users"], "summary": "Verify user account", "parameters": [{"description": "Verification token", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/dto.VerifyAccountRequest"}}], "responses": {"200": {"description": "Account verified successfully", "schema": {"type": "string"}}, "400": {"description": "Bad request", "schema": {"type": "object", "additionalProperties": true}}, "401": {"description": "Invalid token", "schema": {"type": "object", "additionalProperties": true}}, "500": {"description": "Internal server error", "schema": {"type": "object", "additionalProperties": true}}}}}}, "definitions": {"dto.CreateUserDto": {"type": "object", "required": ["email", "name", "password", "premise_id", "role"], "properties": {"email": {"type": "string", "maxLength": 255}, "name": {"type": "string", "maxLength": 100, "minLength": 2}, "password": {"type": "string", "maxLength": 100, "minLength": 6}, "premise_id": {"type": "string"}, "role": {"type": "string"}}}, "dto.LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"email": {"type": "string"}, "password": {"type": "string"}}}, "dto.LoginResponse": {"type": "object", "properties": {"token": {"type": "string"}}}, "dto.UserListResponse": {"type": "object", "properties": {"data": {"type": "array", "items": {"$ref": "#/definitions/models.User"}}, "pagination": {"$ref": "#/definitions/types.Pagination"}}}, "dto.ValidateTokenRequest": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}, "dto.ValidateTokenResponse": {"type": "object", "properties": {"valid": {"type": "boolean"}}}, "dto.VerifyAccountRequest": {"type": "object", "required": ["token"], "properties": {"token": {"type": "string"}}}, "models.User": {"type": "object", "properties": {"created_at": {"type": "string"}, "email": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "name": {"type": "string"}, "role": {"type": "string"}, "updated_at": {"type": "string"}}}, "types.Pagination": {"type": "object", "properties": {"limit": {"type": "integer"}, "page": {"type": "integer"}, "total_pages": {"type": "integer"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}}