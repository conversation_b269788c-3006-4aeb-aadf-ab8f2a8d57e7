basePath: /api/v1
definitions:
  dto.CreateUserDto:
    properties:
      email:
        maxLength: 255
        type: string
      name:
        maxLength: 100
        minLength: 2
        type: string
      password:
        maxLength: 100
        minLength: 6
        type: string
      premise_id:
        type: string
      role:
        type: string
    required:
    - email
    - name
    - password
    - premise_id
    - role
    type: object
  dto.LoginRequest:
    properties:
      email:
        type: string
      password:
        type: string
    required:
    - email
    - password
    type: object
  dto.LoginResponse:
    properties:
      token:
        type: string
    type: object
  dto.UserListResponse:
    properties:
      data:
        items:
          $ref: '#/definitions/models.User'
        type: array
      pagination:
        $ref: '#/definitions/types.Pagination'
    type: object
  dto.ValidateTokenRequest:
    properties:
      token:
        type: string
    required:
    - token
    type: object
  dto.ValidateTokenResponse:
    properties:
      valid:
        type: boolean
    type: object
  dto.VerifyAccountRequest:
    properties:
      token:
        type: string
    required:
    - token
    type: object
  models.User:
    properties:
      created_at:
        type: string
      email:
        type: string
      id:
        type: string
      is_active:
        type: boolean
      name:
        type: string
      role:
        type: string
      updated_at:
        type: string
    type: object
  types.Pagination:
    properties:
      limit:
        type: integer
      page:
        type: integer
      total_pages:
        type: integer
    type: object
host: localhost:8080
info:
  contact:
    email: <EMAIL>
    name: API Support
    url: http://www.swagger.io/support
  description: This is the SCS User Service API for managing users and authentication
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: SCS User Service API
  version: "1.0"
paths:
  /auth/login:
    post:
      consumes:
      - application/json
      description: Authenticate user with email and password
      parameters:
      - description: Login credentials
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.LoginRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Login successful
          schema:
            $ref: '#/definitions/dto.LoginResponse'
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: User login
      tags:
      - auth
  /auth/validate-token:
    post:
      consumes:
      - application/json
      description: Validate if a JWT token is valid and not expired
      parameters:
      - description: Token to validate
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.ValidateTokenRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Token validation result
          schema:
            $ref: '#/definitions/dto.ValidateTokenResponse'
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Invalid token
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Validate JWT token
      tags:
      - auth
  /health:
    get:
      consumes:
      - application/json
      description: Check if the service is running and healthy
      produces:
      - application/json
      responses:
        "200":
          description: Service is healthy
          schema:
            additionalProperties:
              type: string
            type: object
      summary: Health check
      tags:
      - health
  /users:
    get:
      consumes:
      - application/json
      description: Retrieve a paginated list of all users
      parameters:
      - default: 1
        description: Page number
        in: query
        name: page
        type: integer
      - default: 10
        description: Number of items per page
        in: query
        name: limit
        type: integer
      produces:
      - application/json
      responses:
        "200":
          description: Users retrieved successfully
          schema:
            $ref: '#/definitions/dto.UserListResponse'
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get paginated list of users
      tags:
      - users
    post:
      consumes:
      - application/json
      description: Create a new user account with the provided information
      parameters:
      - description: User creation data
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.CreateUserDto'
      produces:
      - application/json
      responses:
        "201":
          description: User created successfully
          schema:
            $ref: '#/definitions/models.User'
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "409":
          description: User already exists
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Create a new user
      tags:
      - users
  /users/me:
    get:
      consumes:
      - application/json
      description: Get the profile information of the currently authenticated user
      produces:
      - application/json
      responses:
        "200":
          description: User profile retrieved successfully
          schema:
            $ref: '#/definitions/models.User'
        "401":
          description: Unauthorized
          schema:
            additionalProperties: true
            type: object
        "404":
          description: User not found
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      security:
      - BearerAuth: []
      summary: Get current user profile
      tags:
      - users
  /users/verify:
    post:
      consumes:
      - application/json
      description: Verify a user account using a verification token
      parameters:
      - description: Verification token
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/dto.VerifyAccountRequest'
      produces:
      - application/json
      responses:
        "200":
          description: Account verified successfully
          schema:
            type: string
        "400":
          description: Bad request
          schema:
            additionalProperties: true
            type: object
        "401":
          description: Invalid token
          schema:
            additionalProperties: true
            type: object
        "500":
          description: Internal server error
          schema:
            additionalProperties: true
            type: object
      summary: Verify user account
      tags:
      - users
securityDefinitions:
  BearerAuth:
    description: Type "Bearer" followed by a space and JWT token.
    in: header
    name: Authorization
    type: apiKey
swagger: "2.0"
